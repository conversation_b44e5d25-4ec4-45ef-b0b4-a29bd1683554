<script setup lang="ts">
// 方案互动-其他方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['otherPriceEmit', 'schemeOtherEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

watch(
  () => [props.demandInfo, props.schemeCacheInfo],
  () => {
    console.log('%c [ 其他方案 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.demandInfo.others);

    oldSchemeList.value = JSON.parse(JSON.stringify(props.demandInfo))?.others || [];

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheInfo?.others || [];

      newSchemeList.value.forEach((e) => {
        // 确保缓存数据包含所有必要字段
        if (!e.hasOwnProperty('invoiceTempId')) e.invoiceTempId = null;
        if (!e.hasOwnProperty('statementTempId')) e.statementTempId = null;
        if (!e.hasOwnProperty('sourceId')) e.sourceId = null;
        if (!e.hasOwnProperty('miceSchemeOtherId')) e.miceSchemeOtherId = null;
        if (!e.hasOwnProperty('billNum')) e.billNum = e.num;
        if (!e.hasOwnProperty('billTotalPrice')) e.billTotalPrice = e.demandTotalPrice;

        e.schemeTotalPrice = e.demandTotalPrice;
      });
    } else {
      const demandData = JSON.parse(JSON.stringify(props.demandInfo))?.others || [];
      newSchemeList.value = demandData.map((e) => {
        return {
          // 临时id字段，暂时为空
          invoiceTempId: null,
          statementTempId: null,
          sourceId: null,

          // 需求相关字段
          miceDemandOtherId: e.id,
          miceSchemeOtherId: null,

          demandDate: e.demandDate,
          itemName: e.itemName,
          num: e.num, // 方案数量
          billNum: e.num, // 账单数量，初始值与方案数量相同
          unit: e.unit,
          specs: e.specs,
          description: e.description,

          // 价格相关字段
          demandTotalPrice: e.demandTotalPrice,
          schemeTotalPrice: e.demandTotalPrice,
          billTotalPrice: e.demandTotalPrice, // 账单总金额，初始值与方案总金额相同
        };
      });
    }

    // 小计 - 使用账单总金额计算
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (e.billTotalPrice) {
        subtotal.value += e.billTotalPrice;
      }
    });

    emit('otherPriceEmit', subtotal.value);
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['项目', '数量', '单位', '总预算', '规则说明'];
const billPlanLabelList = ['项目', '账单数量', '单位', '账单总金额', '规则说明'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice = newSchemeList.value[index].biddingPrice * newSchemeList.value[index].num;
  }

  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('otherPriceEmit', subtotal.value);
  }
};

// 暂存
const otherTempSave = () => {
  emit('schemeOtherEmit', [...newSchemeList.value]);
};

// 校验
const otherSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '其他' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    otherTempSave();
  }

  return isVerPassed;
};

defineExpose({ otherSub, otherTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 其他方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>其他方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.itemName || '-' }}
                </template>
                {{ item.itemName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.num || '-' }}
                </template>
                {{ item.num || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
                </template>
                {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.itemName || '-' }}
                </template>
                {{ item.itemName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.num || '-' }}
                </template>
                {{ item.num || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                </template>
                {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeTotalPrice ? '¥' + formatNumberThousands(item.schemeTotalPrice) : '-' }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeTotalPrice">
                {{ item.schemeTotalPrice + '(元/总预算)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
